<?php

if (!(defined('YMDZCOM'))) {
	exit('Access Denied');
}
class statistic_other extends comm
{
	public function __construct($data)
	{
		$this->data = $data;
	}
	public function GetMyMerchant()
	{
		$data = $this->GetDBSlave1()->queryrow('SELECT * ' . ' FROM ' . TABLE_ADMIN_USERCODE . ' ' . ' WHERE usercode=\'' . $_SESSION['usercode'] . '\'');

		return $data;
	}
	public function GetStatisticMoneyList($page_size)
	{
		//var_dump($this->data);
		//exit();
		$where = $canshu = '';
		$info = array();
		$orderby = 'ORDER BY F.other_usercode,F.log_date DESC';
		if (isset($this->data['type']) && ($this->data['type'] == 'come')) {
			$where .= ' AND F.usercode=\'' . $_SESSION['usercode'] . '\' ' . ' AND F.other_usercode!=F.usercode ' . ' AND F.other_usercode>0 ';
			$canshu .= '&type=come';
		}
		else {
			$where .= ' AND F.other_usercode=\'' . $_SESSION['usercode'] . '\' ' . ' AND F.other_usercode!=F.usercode ' . ' AND F.other_usercode>0 ';
			$canshu .= '&type=to';
		}

		if (isset($this->data['card_Serial']) && !(empty($this->data['card_Serial']))) {
			$where .= ' AND F.card_Serial=\'' . $this->data['card_Serial'] . '\'';
			$canshu .= '&card_Serial=' . $this->data['card_Serial'];
		}

		if (isset($this->data['merchant_id']) && !(empty($this->data['merchant_id']))) {
           if (isset($this->data['type']) && ($this->data['type'] == 'come')) {
			//$this->data['merchant_id'] = $this->GetDBSlave1()->queryrow("SELECT usercode FROM ". TABLE_ADMIN_USERCODE ." WHERE merchant ='".$this->data['merchant_id']."'");	
			//$this->data['merchant_id'] = "100126";
			$usercode_other=$this->GetDBSlave1()->queryrow("SELECT usercode FROM ". TABLE_ADMIN_USERCODE ." WHERE merchant ='".$this->data['merchant_id']."'");
			//$where .= ' AND F.usercode=\'' . $this->data['merchant_id'] . '\'';
			$where .= ' AND F.other_usercode=\'' . $usercode_other['usercode'] . '\'';
			$canshu .= '&merchant_id=' . $this->data['merchant_id'];
			//var_dump($usercode_other['usercode']);
            }else{
			//$this->data['merchant_id'] = $this->GetDBSlave1()->queryrow("SELECT usercode FROM ". TABLE_ADMIN_USERCODE ." WHERE merchant ='".$this->data['merchant_id']."'");	
			//$this->data['merchant_id'] = "100126";
			$usercode=$this->GetDBSlave1()->queryrow("SELECT usercode FROM ". TABLE_ADMIN_USERCODE ." WHERE merchant ='".$this->data['merchant_id']."'");
			//$where .= ' AND F.usercode=\'' . $this->data['merchant_id'] . '\'';
			$where .= ' AND F.usercode=\'' . $usercode['usercode'] . '\'';
			$canshu .= '&merchant_id=' . $this->data['merchant_id'];
			//var_dump($usercode_other['usercode']);
               }

		}

		if (isset($this->data['phone']) && !(empty($this->data['phone']))) {
			$where .= ' AND U.phone=\'' . $this->data['phone'] . '\'';
			$canshu .= '&phone=' . $this->data['phone'];
		}

		if (empty($this->data['stime']) && empty($this->data['etime']) && ($page_size == 24)) {
			$where .= ' AND F.log_date>=\'' . date('Y-m-d') . ' 00:00:00\' AND F.log_date<=\'' . date('Y-m-d') . ' 23:59:59\'';
			$canshu .= '&etime=' . date('Y-m-d') . '&stime=' . date('Y-m-d');			
		}
		else {
			if (isset($this->data['stime']) && !(empty($this->data['stime']))) {
				$where .= ' AND F.log_date>=\'' . $this->data['stime'] . '\'';
				$canshu .= '&stime=' . $this->data['stime'];
			}

			if (isset($this->data['etime']) && !(empty($this->data['etime']))) {
				$where .= ' AND F.log_date<=\'' . $this->data['etime'] . '\'';
				$canshu .= '&etime=' . $this->data['etime'];
			}
		}

		if (isset($this->data['type_id']) && !(empty($this->data['type_id'])) && is_numeric($this->data['type_id'])) {
			$where .= ' AND F.type_id=\'' . $this->data['type_id'] . '\'';
			$canshu .= '&type_id=' . $this->data['type_id'];
			$type = $this->GetDBSlave1()->queryrow('SELECT * FROM ' . TABLE_TYPE_FEE . ' AS F' . ' WHERE F.type_id=\'' . $this->data['type_id'] . '\'');
			$res = $this->GetDBSlave1()->queryrow('SELECT SUM(balance) as total FROM ' . TABLE_LOG_USER_FEE . ' AS F' . ' WHERE F.usercode=\'' . $_SESSION['usercode'] . '\' ' . ' AND F.type_id=\'' . $this->data['type_id'] . '\' ' . ' ' . $where . '');
			$info['total'] = $res['total'];
			$info['type_name'] = $type['type_name'];
		}

		if (isset($this->data['machine_id']) && !(empty($this->data['machine_id'])) && is_numeric($this->data['machine_id'])) {
			$where .= ' AND F.machine_id =\'' . $this->data['machine_id'] . '\' ';
			$canshu .= '&machine_id=' . $this->data['machine_id'];
		}

		$gift_balance = $this->GetDBSlave1()->queryrow('SELECT COUNT(*) AS count,IFNULL(SUM(gift_balance),0) AS total FROM ' . TABLE_LOG_USER_FEE . ' AS F ' . ' LEFT JOIN ' . TABLE_USER . '  AS U ON F.userid=U.userid ' . ' WHERE F.other_usercode!=F.usercode ' . ' AND F.gift_balance>0 ' . ' AND F.user_gift_balance>F.after_gift_balance' . ' ' . $where . ' ');
		//获取当前威信跨运营商消费
		$wx_balance = $this->GetDBSlave1()->queryrow('SELECT COUNT(*) AS count,IFNULL(SUM(F.balance),0) AS total FROM ' . TABLE_LOG_USER_FEE . ' AS F ' . ' LEFT JOIN ' . TABLE_USER . '  AS U ON F.userid=U.userid ' . ' WHERE F.other_usercode!=F.usercode ' . ' AND F.balance>0 ' . '   AND F.type_id=14' . '' . $where . ' ');

			

		//获取当前余额跨运营商消费
		$balance = $this->GetDBSlave1()->queryrow('SELECT COUNT(*) AS count,IFNULL(SUM(F.balance),0) AS total FROM ' . TABLE_LOG_USER_FEE . ' AS F ' . ' LEFT JOIN ' . TABLE_USER . '  AS U ON F.userid=U.userid ' . ' WHERE F.other_usercode!=F.usercode ' . ' AND F.balance>0 ' . '   AND F.type_id=10' . '' . $where . ' ');


		$MoreTotal = array('gift_balance' => $gift_balance, 'balance' => $balance);

		$WxTotal = array('gift_balance' => $gift_balance, 'balance' => $wx_balance);

		$total = $this->GetDBSlave1()->queryrow('SELECT COUNT(*) as total FROM ' . TABLE_LOG_USER_FEE . ' AS F ' . ' LEFT JOIN ' . TABLE_USER . '  AS U ON F.userid=U.userid ' . ' WHERE F.other_usercode!=F.usercode ' . $where . ' ');
		if (!(isset($_REQUEST['curpage'])) || !(is_numeric($_REQUEST['curpage'])) || ($_REQUEST['curpage'] < 1)) {
			$curpage = 1;
		}
		else {
			$curpage = intval($_REQUEST['curpage']);
		}

		if (isset($_REQUEST['page_size'])) {
			$page_size = intval($_REQUEST['page_size']);
		}


		//$array = $this->GetDBSlave1()->queryrows('SELECT F.*,F.user_balance as before_balance,F.user_gift_balance AS before_gift_balance,F.after_gift_balance ,F.gift_balance,F.user_point AS before_point,M.machine_name,U.phone,U.username FROM ' . ' ' . TABLE_LOG_USER_FEE . ' AS F ' . ' LEFT JOIN ' . TABLE_MACHINE . ' AS M ON F.machine_id=M.machine_id ' . ' LEFT JOIN ' . TABLE_USER . '  AS U ON F.userid=U.userid ' . ' WHERE F.other_usercode!=F.usercode ' . ' AND F.other_usercode>0' . '  ' . $where . ' ' . $orderby . ' LIMIT ' . (($curpage - 1) * $page_size) . ',' . $page_size . ' ');
		//SELECT F.*,F.user_balance as before_balance,F.user_gift_balance AS before_gift_balance,F.after_gift_balance ,F.gift_balance,F.user_point AS before_point,M.machine_name,U.phone,U.username,U.vip_count FROM log_user_fee AS F LEFT JOIN comm_machine AS M ON F.machine_id=M.machine_id LEFT JOIN comm_user AS U ON F.userid=U.userid WHERE F.other_usercode!=F.usercode AND F.other_usercode>0 AND F.other_usercode='100134' AND F.other_usercode!=F.usercode AND F.other_usercode>0 AND F.usercode='1530946347' AND F.log_date>='2019-03-24 00:00:00' AND F.log_date<='2019-03-24 23:59:59' ORDER BY F.other_usercode,F.log_date DESC LIMIT 0,24
		$array = $this->GetDBSlave1()->queryrows('
			SELECT F.*,F.user_balance as before_balance,F.user_gift_balance AS before_gift_balance,F.after_gift_balance ,F.gift_balance,F.user_point AS before_point,M.machine_name,U.phone,U.username,U.vip_count FROM ' . ' ' . TABLE_LOG_USER_FEE . ' AS F ' . ' LEFT JOIN ' . TABLE_MACHINE . ' AS M ON F.machine_id=M.machine_id ' . ' LEFT JOIN ' . TABLE_USER . '  AS U ON F.userid=U.userid ' . ' WHERE F.other_usercode!=F.usercode ' . ' AND F.other_usercode>0' . ' ' . $where . ' ' . $orderby . ' LIMIT ' . (($curpage - 1) * $page_size) . ',' . $page_size . ' 
			 ');
/*
 var_dump('
			SELECT F.*,F.user_balance as before_balance,F.user_gift_balance AS before_gift_balance,F.after_gift_balance ,F.gift_balance,F.user_point AS before_point,M.machine_name,U.phone,U.username,U.vip_count FROM ' . ' ' . TABLE_LOG_USER_FEE . ' AS F ' . ' LEFT JOIN ' . TABLE_MACHINE . ' AS M ON F.machine_id=M.machine_id ' . ' LEFT JOIN ' . TABLE_USER . '  AS U ON F.userid=U.userid ' . ' WHERE F.other_usercode!=F.usercode ' . ' AND F.other_usercode>0' . ' ' . $where . ' ' . $orderby . ' LIMIT ' . (($curpage - 1) * $page_size) . ',' . $page_size . ' 
			 ');*/
		return array('data' => $array, 'total' => $total['total'], 'curpage' => $curpage, 'page_size' => $page_size, 'canshu' => $canshu, 'array' => $info, 'MoreTotal' => $MoreTotal,'WxTotal'=>$WxTotal);

	}

	public function GetStatisticDay($type = 1)
	{
		$where = '';
		$time = '';
		$canshu = '';
		if (isset($this->data['type']) && ($this->data['type'] == 'come')) {
			$where .= ' AND F.other_usercode=\'' . $_SESSION['usercode'] . '\' ' . ' AND F.other_usercode!=F.usercode ' . ' AND F.other_usercode>0 ';
			$canshu .= '&type=come';
		}
		else {
			$where .= ' AND F.usercode=\'' . $_SESSION['usercode'] . '\' ' . ' AND F.other_usercode!=F.usercode ' . ' AND F.other_usercode>0 ';
			$canshu .= '&type=come';
		}

		if ($type == 1) {
			if (isset($this->data['indate']) && !(empty($this->data['indate']))) {
				$today = $this->data['indate'];
			}
			else {
				$today = date('Y-m-d', time());
			}

			$stime = $today . ' ' . '00:00:00';
			$etime = $today . ' ' . '23:59:59';
			$time .= $today;
			$where .= ' AND log_date>=\'' . $stime . '\' AND  log_date<=\'' . $etime . '\'';
		}
		else if ($type == 2) {
			if (isset($this->data['indate']) && !(empty($this->data['indate']))) {
				$indate = $this->data['indate'];
				$start = $indate . '-01';
				$over = $indate . '-' . date('t', time());
				$time .= $indate;
			}
			else {
				$start = date('Y-m-01', time());
				$over = date('Y-m-d', time());
				$time .= date('Y-m', time());
			}

			$stime = $start . ' ' . '00:00:00';
			$etime = $over . ' ' . '23:59:59';
			$where .= ' AND log_date>=\'' . $stime . '\' AND  log_date<=\'' . $etime . '\'';
		}
		else {
			if (isset($this->data['card_Serial']) && !(empty($this->data['card_Serial']))) {
				$where .= ' AND card_Serial LIKE \'%' . $this->data['card_Serial'] . '%\'';
			}

			if (isset($this->data['stime']) && !(empty($this->data['stime']))) {
				$where .= ' AND log_date>=\'' . $this->data['stime'] . '\'';
			}

			if (isset($this->data['etime']) && !(empty($this->data['etime']))) {
				$where .= ' AND log_date<=\'' . $this->data['etime'] . '\'';
			}

			if (isset($this->data['machine_id']) && !(empty($this->data['machine_id'])) && is_numeric($this->data['machine_id'])) {
				$where .= ' AND machine_id =\'' . $this->data['machine_id'] . '\'';
			}

			if (isset($this->data['type_id']) && !(empty($this->data['type_id'])) && is_numeric($this->data['type_id'])) {
				$where .= ' AND type_id=\'' . $this->data['type_id'] . '\'';
			}

			if (empty($this->data['stime']) || empty($this->data['etime']) || (93 < floor((strtotime($this->data['etime']) - strtotime($this->data['stime'])) / 86400))) {
				return array('time' => $time, 'data' => '', 'msg' => _ADMIN_28);
			}

			$where .= '';
			$time .= '';
		}

		$data = $this->GetDBSlave1()->queryrows('SELECT * FROM ' . TABLE_LOG_USER_FEE . ' AS F' . ' WHERE  1  ' . ' ' . $where . ' ' . ' order by log_date DESC');

		if (!($data)) {
			return array('time' => $time, 'data' => '');
		}

		$array = array();
		$type = $this->GetDBSlave1()->queryrows('SELECT * FROM ' . TABLE_TYPE_FEE . ' ');

		for ($i = 0; $i < count($type); $i++) {
			foreach ($data as $item ) {
				if ($type[$i]['type_id'] == $item['type_id']) {
					$array[$type[$i]['type_id']]['order'][] = $item;
				}

				$array[$type[$i]['type_id']]['type_name'] = $type[$i]['type_name'];
				$array[$type[$i]['type_id']]['money'] = 0;
			}
		}

		foreach ($array as $k => &$v ) {
			if (!(empty($v['order'])) && (0 < count($v['order']))) {
				for ($i = 0; $i < count($v['order']); $i++) {
					$v['total'] = $i + 1;
					$v['money'] += $v['order'][$i]['balance'];
				}

				unset($v['order']);
			}
			else {
				$v['total'] = 0;
			}
		}

		return array('time' => $time, 'data' => $array);
	}

	public function GetStatisticGather()
	{
		$data = $this->GetStatisticDay(3);
		return $data;
	}

	public function GetAdminList()
	{
		$data = $this->GetDBSlave1()->queryrows('SELECT admin_id,admin_name FROM ' . TABLE_ADMIN . ' ' . ' WHERE usercode=\'' . $_SESSION['usercode'] . '\'');
		return $data;
	}

	public function GetUsercodeFromOperator()
	{
		return $admin = $this->GetDBSlave1()->queryrow('SELECT U.admin_id,U.fid,' . "\r\n" . '        U.merchant_key,U.usercode,U.admin_name,' . "\r\n" . '        U.sms_allow_count,U.sms_count' . ' ' . ' FROM ' . TABLE_ADMIN . ' AS A ' . ' LEFT JOIN ' . TABLE_ADMIN_USERCODE . ' AS U ON A.usercode=U.usercode' . ' WHERE A.admin_id=\'' . $_SESSION['admin_id'] . '\'');
	}

	public function GetMerchantList()
	{
		$my_key = $this->GetUsercodeFromOperator();
		
		//var_dump($my_key);
		//exit();
		//var_dump("SELECT U.merchant,U.admin_name,U.company_name,U.usercode FROM ". TABLE_ADMIN_USERCODE ." WHERE usercode !='".$_SESSION['usercode']."'AND merchant_key = '".$my_key['merchant_key'] . "'AND merchant_key>0");

		$allkey = $this->GetDBSlave1()->queryrows("SELECT merchant,admin_name,company_name,usercode FROM ". TABLE_ADMIN_USERCODE ." WHERE usercode !='".$_SESSION['usercode']."'AND merchant_key = '".$my_key['merchant_key'] . "'AND merchant_key>0");
		return $allkey;
	}
}


?>
